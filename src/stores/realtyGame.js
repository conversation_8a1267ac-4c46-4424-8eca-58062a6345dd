import { defineStore } from 'pinia'

export const useRealtyGameStore = defineStore('realtyGame', {
  state: () => ({
    gameListings: [],
    gameTitle: 'Property Price Challenge',
    gameDesc: '',
    gameBgImageUrl: '',
    currentProperty: null,
    isDataLoaded: false,
  }),
  actions: {
    setRealtyGameData(data) {
      this.gameListings = data.gameListings || []
      this.gameTitle = data.gameTitle || 'Property Price Challenge'
      this.gameDesc = data.gameDesc || ''
      this.gameBgImageUrl = data.gameBgImageUrl || ''
      this.currentProperty = data.currentProperty || null
      this.isDataLoaded = true
    },
    clearRealtyGameData() {
      this.gameListings = []
      this.gameTitle = 'Property Price Challenge'
      this.gameDesc = ''
      this.gameBgImageUrl = ''
      this.currentProperty = null
      this.isDataLoaded = false
    },
    getPropertyByIndex(index) {
      return this.gameListings[index] || null
    },
    getPropertyByUuid(uuid) {
      return this.gameListings.find((prop) => prop.uuid === uuid) || null
    },
  },
  getters: {
    getGameListings: (state) => state.gameListings,
    getGameTitle: (state) => state.gameTitle,
    getGameDesc: (state) => state.gameDesc,
    getGameBgImageUrl: (state) => state.gameBgImageUrl,
    getCurrentProperty: (state) => state.currentProperty,
    getTotalProperties: (state) => state.gameListings.length,
    getIsDataLoaded: (state) => state.isDataLoaded,
  },
})
