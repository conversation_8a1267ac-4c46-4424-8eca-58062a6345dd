const routes = [
  {
    path: '/',
    component: () => import('src/concerns/hpg/layouts/HpgLayout.vue'),
    //  layouts/htoc-gate/HtocGateLayout.vue'),
    children: [
      {
        path: '',
        name: 'rSubdomainRoot',
        component: () => import('src/concerns/hpg/pages/HpgHomePage2025.vue'),
        meta: {
          title: 'Property Price Challenge - Test Your Property Knowledge',
          description:
            'Challenge yourself with our interactive property price guessing game. Test your knowledge of local property values and improve your market understanding.',
          keywords:
            'property prices, real estate game, property valuation, house prices, property market',
          ogType: 'website',
          ogImage:
            'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
          twitterCard: 'summary_large_image',
        },
      },
      {
        path: 'meta-test',
        name: 'rMetaTagsTest',
        component: () => import('src/pages/MetaTagsTestPage.vue'),
        meta: {
          title: 'Meta Tags Test Page',
          description: 'Test page for checking meta tags functionality',
        },
      },
      {
        path: 'buying-steps',
        name: 'rBuyingsteps',
        component: () =>
          import('src/concerns/buyers-steps/pages/BuyersStepsLandingPage.vue'),
        meta: {
          title: 'Home Buying Steps Guide - Complete Property Purchase Process',
          description:
            'Step-by-step guide to buying your first home. Learn the complete property purchase process from search to completion.',
          keywords:
            'home buying, property purchase, first time buyer, buying process, property guide',
          ogType: 'article',
          ogImage:
            'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
        },
      },
      {
        path: 'price-guess-session',
        name: 'rPriceGuessSession',
        component: () =>
          import('src/concerns/price-guess/pages/PriceGuessSession.vue'),
        meta: {
          title: 'Property Price Game Session - Active Game',
          description:
            'Active property price guessing game session. Test your knowledge of local property values.',
          keywords: 'property game, price guessing, real estate quiz',
          ogType: 'website',
          robots: 'noindex, nofollow', // Game session
        },
      },
      {
        // TODO - implement below
        path: 'price-guess-tokens',
        name: 'rPriceGuessTokens',
        component: () =>
          import('src/concerns/price-guess/pages/InitPriceGuessPage.vue'),
        meta: {
          title: 'Property Price Game Tokens',
          description: 'Property price guessing game with token system.',
          robots: 'noindex, nofollow', // Not implemented
        },
      },
      {
        path: 'games',
        name: 'rPriceGames',
        component: () =>
          import('src/concerns/psq/components/ScootGamesSummary.vue'),
        meta: {
          title: 'Property Price Challenge Games',
          description:
            'Choose from our collection of property price guessing games. Test your knowledge of local property markets.',
          keywords:
            'property games, price challenge, real estate quiz, property valuation game',
        },
      },
      {
        path: 'price-game',
        name: 'rPriceGameStartDefault',
        // will need to rethink below as default will not always
        // be "regular-game"
        redirect: '/price-game/regular-game', // Redirect to a default usrId
        meta: {
          title: 'Property Price Challenge Games',
          description:
            'Choose from our collection of property price guessing games. Test your knowledge of local property markets.',
          keywords:
            'property games, price challenge, real estate quiz, property valuation game',
        },
      },
      {
        path: 'clear',
        name: 'rClearLocalStorage',
        component: () =>
          import('src/concerns/realty-game/pages/ClearLocalStorage.vue'),
        meta: {
          title: 'Clear Game Data',
          description: 'Clear your local game data and start fresh.',
          robots: 'noindex, nofollow',
        },
      },
      {
        // 13 june 2025 - price-game routes to replace price-guess
        // routes and use the new realty_game model on the back end
        // 22 june - change from price-game to game for hpg..com
        // as that uses diff backendpoint with game_default_locale as slug
        path: 'game/:gameSlug',
        name: 'rPriceGame',
        component: () =>
          import('src/concerns/realty-game/layouts/RealtyGameLayout.vue'),
        meta: {
          title: 'Property Price Challenge - Interactive Real Estate Game',
          description:
            'Test your property market knowledge with our interactive price guessing game. Challenge yourself with real properties and see how well you know local values.',
          keywords:
            'property price game, real estate challenge, property valuation, house price quiz, market knowledge test',
          ogType: 'website',
          ogImage:
            'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
          twitterCard: 'summary_large_image',
        },
        children: [
          {
            path: '',
            name: 'rPriceGameStart',
            component: () =>
              import('src/concerns/realty-game/pages/RealtyGameStartPage.vue'),
            meta: {
              title:
                'Start Property Price Challenge - Test Your Market Knowledge',
              description:
                'Ready to test your property market knowledge? Start our interactive price guessing game and see how well you know local property values.',
              keywords:
                'start property game, price challenge, real estate quiz, property knowledge test',
              ogType: 'website',
            },
          },
          {
            path: 'superwiser',
            name: 'rPriceGamePropertiesAdmin',
            // june 2025: not convinced below does anything:
            meta: {
              ssr: false, // Exclude from SSR
              title: 'Game Properties Admin - Property Management',
              description:
                'Admin interface for managing game properties and settings.',
              robots: 'noindex, nofollow',
            },
            component: () =>
              import(
                'src/concerns/realty-game/pages/RealtyGamePropertiesAdmin.vue'
              ),
          },
          {
            path: 'admin/create-game',
            name: 'rPriceGameCreateGame',
            component: () =>
              import('src/concerns/price-guess/pages/PriceGuessCreateGame.vue'),
            meta: {
              title: 'Create New Property Game - Game Builder',
              description:
                'Create a new property price guessing game with custom properties and settings.',
              robots: 'noindex, nofollow',
            },
          },
          {
            path: 'property/:propertyUuid',
            name: 'rPriceGameProperty',
            component: () =>
              import(
                'src/concerns/realty-game/pages/RealtyGamePropertyPage.vue'
              ),
            props: (route) => ({
              propertyUuid: route.params.propertyUuid,
              gameSessionId: route.query.session,
            }),
            meta: {
              title: 'Guess Property Price - Property Price Challenge',
              description:
                "Make your best guess at this property's value. Test your knowledge of the local property market.",
              keywords:
                'property price guess, house valuation, property game, real estate quiz',
              ogType: 'website',
              robots: 'noindex, nofollow', // Game session specific
            },
          },
          {
            path: 'results/:gameSessionId',
            name: 'rPriceGameResults',
            component: () =>
              import(
                'src/concerns/realty-game/pages/RealtyGameResultsPage.vue'
              ),
            props: true,
            meta: {
              title: 'Your Property Price Challenge Results',
              description:
                'See how well you performed in the property price challenge. Compare your guesses with actual property values.',
              keywords:
                'property game results, price guess results, property knowledge score',
              ogType: 'website',
              ogImage:
                'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
              robots: 'noindex, nofollow', // Game session specific
            },
          },
          // {
          //   path: 'results_client_calculated/:gameSessionId',
          //   name: 'rPriceGameResultsClientCalculated',
          //   component: () =>
          //     import(
          //       'src/concerns/price-guess/pages/PriceGuessResultsPageClientCalculated.vue'
          //     ),
          //   props: true,
          // },
          // {
          //   path: 'single/property/:propertyUuid',
          //   name: 'rSinglePriceGameProperty',
          //   component: () =>
          //     import(
          //       'src/concerns/price-guess/pages/SinglePriceGuessPropertyPage.vue'
          //     ),
          //   props: (route) => ({
          //     propertyUuid: parseInt(route.params.propertyUuid),
          //     gameSessionId: route.query.session,
          //   }),
          // },
          // {
          //   path: 'single/results/:gameSessionId',
          //   name: 'rSinglePriceGameResults',
          //   component: () =>
          //     import(
          //       'src/concerns/price-guess/pages/SinglePriceGuessResultsPage.vue'
          //     ),
          //   props: true,
          // },
        ],
      },
      {
        path: 'price-guess',
        name: 'rPriceGuessGame',
        component: () =>
          import('src/concerns/price-guess/layouts/PriceGuessGameLayout.vue'),
        meta: {
          title: 'Property Price Guessing Game - Legacy Version',
          description:
            'Legacy version of our property price guessing game. Test your knowledge of local property values.',
          keywords:
            'property price game, real estate quiz, property valuation, legacy game',
          robots: 'noindex, nofollow', // Legacy version
        },
        children: [
          {
            path: '',
            name: 'rPriceGuessStart',
            component: () =>
              import('src/concerns/price-guess/pages/PriceGuessStartPage.vue'),
            meta: {
              title: 'Start Property Price Guessing Game - Legacy',
              description:
                'Start the legacy version of our property price guessing game.',
              robots: 'noindex, nofollow',
            },
          },
          {
            path: 'property/:propertyIndex',
            name: 'rPriceGuessProperty',
            component: () =>
              import(
                'src/concerns/price-guess/pages/PriceGuessPropertyPage.vue'
              ),
            props: (route) => ({
              propertyIndex: parseInt(route.params.propertyIndex),
              gameSessionId: route.query.session,
            }),
          },
          {
            path: 'results/:gameSessionId',
            name: 'rPriceGuessResults',
            component: () =>
              import(
                'src/concerns/price-guess/pages/PriceGuessResultsPage.vue'
              ),
            props: true,
          },
          {
            path: 'results_client_calculated/:gameSessionId',
            name: 'rPriceGuessResultsClientCalculated',
            component: () =>
              import(
                'src/concerns/price-guess/pages/PriceGuessResultsPageClientCalculated.vue'
              ),
            props: true,
          },
          {
            path: 'single/property/:propertyUuid',
            name: 'rSinglePriceGuessProperty',
            component: () =>
              import(
                'src/concerns/price-guess/pages/SinglePriceGuessPropertyPage.vue'
              ),
            props: (route) => ({
              propertyUuid: parseInt(route.params.propertyUuid),
              gameSessionId: route.query.session,
            }),
          },
          {
            path: 'single/results/:gameSessionId',
            name: 'rSinglePriceGuessResults',
            component: () =>
              import(
                'src/concerns/price-guess/pages/SinglePriceGuessResultsPage.vue'
              ),
            props: true,
          },
          {
            path: 'superwiser',
            name: 'rPriceGuessPropertiesAdmin',
            component: () =>
              import(
                'src/concerns/price-guess/pages/PriceGuessPropertiesAdmin.vue'
              ),
          },
          {
            path: 'admin/create-game',
            name: 'rPriceGuessCreateGame',
            component: () =>
              import('src/concerns/price-guess/pages/PriceGuessCreateGame.vue'),
          },
        ],
      },
      {
        path: 'access-error',
        name: 'rSubdomainAccessError',
        component: () => import('src/pages/PpsqSubdomainLandingPage.vue'),
        meta: {
          title: 'Access Error - House Price Guess',
          description:
            'There was an issue accessing this page. Please check your permissions or contact support.',
          robots: 'noindex, nofollow',
        },
      },
      {
        path: 'scoot-management',
        name: 'rScootGameManagement',
        component: () =>
          import('src/concerns/psq/pages/ScootGameManagementPage.vue'),
        meta: {
          title: 'Scoot Game Management - Admin Interface',
          description:
            'Manage scoot data and games for this subdomain. Configure game settings and add/remove games.',
          robots: 'noindex, nofollow',
        },
      },
      {
        path: 'my-dossier',
        name: 'rSubdomainRedir',
        component: () => import('src/pages/HtocSubdomainRedirectPage.vue'),
      },
      {
        path: '0',
        name: 'rUnusedRoot',
        component: () =>
          import(
            'src/concerns/dossiers/pages/landing-experiments/HtocLandingPage.vue'
          ),
      },
      {
        path: 'charts',
        name: 'rInitialCharts',
        component: () => import('src/concerns/charts/pages/ChartTestsPage.vue'),
      },
      {
        path: 'epcs',
        name: 'rInitialEpcs',
        component: () => import('src/concerns/epcs/pages/EpcTestsPage.vue'),
      },
      {
        path: 'chart_examples',
        name: 'rExampleCharts',
        component: () =>
          import('src/concerns/charts/pages/ChartExamplesPage.vue'),
      },
      {
        path: 'chart_examples_2',
        name: 'rExampleCharts2',
        component: () =>
          import('src/concerns/charts/pages/ChartExamplesPage2.vue'),
      },
      {
        path: 'chart_examples_3',
        name: 'rExampleCharts3',
        component: () =>
          import('src/concerns/charts/pages/ChartExamplesPage3.vue'),
      },
      {
        path: 'sold_homes_charts/:targetChartName',
        name: 'rSoldHomesCharts',
        component: () =>
          import('src/concerns/charts/pages/SoldHomesCharts.vue'),
      },
      {
        path: '2',
        name: 'rUnusedRoot2',
        component: () =>
          import(
            'src/concerns/dossiers/pages/landing-experiments/HtocLandingPage2.vue'
          ),
      },
      {
        path: '3',
        name: 'rUnusedRoot3',
        component: () =>
          import(
            'src/concerns/dossiers/pages/landing-experiments/HtocLandingPage3.vue'
          ),
      },
      {
        path: '4',
        name: 'rUnusedRoot4',
        component: () =>
          import(
            'src/concerns/dossiers/pages/landing-experiments/HtocGate2025LandingCta.vue'
          ),
      },
      {
        path: '5',
        name: 'rUnusedRoot5',
        component: () =>
          import(
            'src/concerns/dossiers/pages/landing-experiments/HtocLandingPageBasicFromGpt.vue'
          ),
      },
      {
        path: '6',
        name: 'rUnusedRoot6',
        component: () =>
          import(
            'src/concerns/dossiers/pages/landing-experiments/HtocLandingPage_Dossier_1.vue'
          ),
      },
      {
        path: '7',
        name: 'rUnusedRoot7',
        component: () =>
          import(
            'src/concerns/dossiers/pages/landing-experiments/HtocLandingPage7.vue'
          ),
      },
      // 25 may 2025 - tried creating landing page with augment and
      // it was in a league of its own.
      // So much so that I will add new ones from now in landing-contenders folder
      {
        path: '10',
        name: 'rUnusedRoot10',
        component: () =>
          import(
            'src/concerns/dossiers/pages/landing-contenders/HtocLandingPage10.vue'
          ),
      },
      {
        path: '11',
        name: 'rUnusedRoot11',
        component: () =>
          import(
            'src/concerns/dossiers/pages/landing-contenders/HtocLandingPage11.vue'
          ),
      },
      {
        path: '12',
        name: 'rUnusedRoot12',
        component: () =>
          import(
            'src/concerns/dossiers/pages/landing-contenders/HtocLandingPage12.vue'
          ),
      },
      {
        path: '13',
        name: 'rUnusedRoot13',
        component: () =>
          import(
            'src/concerns/dossiers/pages/landing-contenders/HtocLandingPage13.vue'
          ),
      },
      {
        path: 'reference-properties',
        name: 'rReferenceProperties',
        component: () =>
          import(
            'src/concerns/reference-properties/pages/ReferencePropertiesList.vue'
          ),
        meta: {
          title: 'Reference Properties - Property Market Data',
          description:
            'Browse our collection of reference properties with detailed market data, pricing history, and comparable analysis.',
          keywords:
            'reference properties, property data, market analysis, property comparables, real estate reference',
          ogType: 'website',
          ogImage:
            'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
        },
        // component: () =>
        //   import('pages/htoc-comparables/HtocGateCostaHomePage.vue'),
      },
      {
        path: 'for-sale-evaluation/:saleListingUuid',
        name: 'rForSaleEvalContainer',
        component: () =>
          import(
            'src/concerns/for-sale-evaluations/layouts/ForSaleEvaluationLayout.vue'
          ),
        children: [
          // 23 mar 2025 - can remove this page as it is no longer useful
          // was the starting point for what are now the dossier routes
          {
            path: '',
            name: 'rForSaleEvalDetails',
            component: () =>
              import(
                'src/concerns/for-sale-evaluations/pages/ForSaleEvaluationDetails.vue'
              ),
          },
        ],
      },
      // dossiers below was initally based on for-sale-evaluations above
      {
        path: 'reference-properties/:refPropUuid',
        name: 'rReferencePropertyContainer',
        component: () =>
          import(
            'src/concerns/reference-properties/layouts/SingleReferencePropertyLayout.vue'
          ),
        children: [
          {
            path: '',
            name: 'rReferencePropertyDetails',
            component: () =>
              import(
                'src/concerns/reference-properties/pages/ReferencePropertyDetails.vue'
              ),
          },
        ],
      },
    ],
  },
  // 10 june 2025 - little experiments
  {
    path: '/portal-query-runner',
    name: 'rPortalQueryRunner',
    component: () =>
      import('src/concerns/price-guess/pages/PortalRueryRunner.vue'),
  },
  {
    path: '/buyers-steps-fulla',
    name: 'rBuyersstepsFulla',
    component: () =>
      import('src/concerns/buyers-steps/pages/BuyersStepsLandingPage.vue'),
  },
  {
    path: '/buyers-steps',
    name: 'rBuyerssteps',
    component: () =>
      import('src/concerns/buyers-steps/layouts/BuyersStepsLayout.vue'),
    // import('src/concerns/buyers-steps/pages/BuyersStepsLandingPage.vue'),
  },
  {
    path: '/superwiser/dossiers',
    name: 'rDossierSuperwiser',
    component: () =>
      import(
        'src/concerns/dossiers/containers/SuperwiserDossiersMainContainer.vue'
      ),
  },
  {
    path: '/superwiser/dossier-assets/:dossierAssetId',
    // for viewing listing photos of a dossier asset - in case I need to run plan_b
    name: 'rDossierAssetSuperwiser',
    component: () =>
      import(
        'src/concerns/dossiers/containers/SuperwiserDossierAssetMainContainer.vue'
      ),
  },
  {
    path: '/rawdossiers/:dossierUuid',
    name: 'rRawDossierContainer',
    component: () =>
      import('src/concerns/dossiers/containers/DossiersMainContainer.vue'),
    // These raw routes needed initially so I can create a screenshot
    // using playwright that can be passed to an LLM
    children: [
      {
        path: 'photos',
        name: 'rRawDossierPhotos',
        component: () =>
          import('src/concerns/dossiers/pages/RawDossierPhotos.vue'),
      },
      {
        path: 'listing_photos/:listingUuid',
        name: 'rRawDossierListingPhotos',
        component: () =>
          import('src/concerns/dossiers/pages/RawDossierListingPhotos.vue'),
      },
      {
        path: 'overview',
        name: 'rRawDossierOverview',
        component: () =>
          import('src/concerns/dossiers/pages/RawDossierSections.vue'),
      },
    ],
  },
  {
    path: '/dossiers/:dossierUuid',
    name: 'rDossierContainer',
    component: () =>
      import('src/concerns/dossiers/containers/DossiersMainContainer.vue'),
    meta: {
      title: 'Property Dossier - Comprehensive Property Analysis',
      description:
        'Detailed property analysis including market data, comparables, photos, and neighborhood insights. Complete property intelligence for informed decisions.',
      keywords:
        'property dossier, property analysis, market data, property intelligence, real estate research',
      ogType: 'website',
      ogImage:
        'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
      twitterCard: 'summary_large_image',
    },
    children: [
      {
        path: '',
        name: 'rDossierDetails',
        component: () =>
          import(
            'src/concerns/dossiers/containers/DossiersDrawerContainer.vue'
          ),
        redirect: { name: 'rDossierHome' }, // Add this line to redirect
        meta: {
          title: 'Property Dossier Dashboard',
          description:
            'Access your complete property dossier with market analysis, photos, and neighborhood data.',
          keywords: 'property dashboard, dossier home, property analysis',
        },

        // 20 mar 2025 - replacing tabs nav with drawer nav
        // component: () =>
        //   import(
        //     'src/concerns/dossiers/containers/DossiersTabsContainer.vue'
        //   ),
        children: [
          {
            path: 'home',
            name: 'rDossierHome',
            component: () =>
              import('src/concerns/dossiers/pages/DossierHome.vue'),
            meta: {
              title: 'Property Dossier Home - Overview & Summary',
              description:
                'Property dossier home page with overview, key metrics, and quick access to detailed analysis.',
              keywords: 'property overview, dossier home, property summary',
            },
          },
          {
            path: 'overview',
            name: 'rDossierOverview',
            component: () =>
              import('src/concerns/dossiers/pages/DossierDetails.vue'),
            meta: {
              title: 'Property Overview - Detailed Property Information',
              description:
                'Comprehensive property overview including specifications, features, and key details.',
              keywords:
                'property overview, property details, property specifications',
            },
          },
          {
            path: 'photos',
            name: 'rDossierPhotos',
            component: () =>
              import('src/concerns/dossiers/pages/DossierPhotos.vue'),
            meta: {
              title: 'Property Photos - High Quality Property Images',
              description:
                'Browse high-quality photos of this property including interior, exterior, and detail shots.',
              keywords:
                'property photos, property images, real estate photography',
            },
          },
          {
            path: 'photos/edit',
            name: 'rDossierPhotosEdit',
            component: () =>
              import('src/concerns/dossiers/pages/DossierPhotos.vue'),
          },
          {
            path: 'ai-feedback',
            name: 'rDossierLlmFeedback',
            component: () =>
              import('src/concerns/dossiers/pages/AiFeedbackPanel.vue'),
          },
          {
            path: 'calendar',
            name: 'rTaskCalendar',
            component: () =>
              import('src/concerns/dossiers/components/Calendar.vue'),
          },
          {
            path: 'notifications',
            name: 'rNotifications',
            component: () =>
              import('src/concerns/dossiers/components/Notifications.vue'),
          },
          {
            path: 'ai-chat',
            name: 'rConversations',
            component: () =>
              import('src/concerns/dossiers/components/AiChat.vue'),
          },
          {
            path: 'profile',
            name: 'rHunterProfile',
            component: () =>
              import('src/concerns/dossiers/pages/HunterProfile.vue'),
          },
          {
            path: 'profile/edit',
            name: 'rHunterProfileEdit',
            component: () =>
              import('src/concerns/dossiers/pages/HunterProfileEdit.vue'),
          },
          {
            path: 'notes-and-queries',
            name: 'rNotesAndQueries',
            component: () =>
              import('src/concerns/dossiers/components/NotesAndQueries.vue'),
            children: [
              {
                path: 'create',
                name: 'rCreateNoteOrQuery',
                component: () =>
                  import('src/concerns/dossiers/components/jots/CreateJot.vue'),
                children: [
                  {
                    path: 'primary',
                    name: 'rCreateNoteOrQueryPrimary',
                    component: () =>
                      import(
                        'src/concerns/dossiers/components/jots/CreateJot.vue'
                      ),
                    meta: { preSelectProperty: 'primary' },
                  },
                  {
                    path: 'comparison/:comparisonUuid',
                    name: 'rCreateNoteOrQueryComparison',
                    component: () =>
                      import(
                        'src/concerns/dossiers/components/jots/CreateJot.vue'
                      ),
                    props: true,
                    meta: { preSelectProperty: 'comparison' },
                  },
                ],
              },
              {
                path: 'primary',
                name: 'rNotesAndQueriesPrimary',
                component: () =>
                  import(
                    'src/concerns/dossiers/components/NotesAndQueries.vue'
                  ),
                meta: { filterType: 'primary' },
              },
              {
                path: 'comparison/:comparisonUuid',
                name: 'rNotesAndQueriesComparison',
                component: () =>
                  import(
                    'src/concerns/dossiers/components/NotesAndQueries.vue'
                  ),
                props: true,
                meta: { filterType: 'comparison' },
              },
            ],
          },
          {
            path: 'tasks',
            name: 'rTasks',
            component: () =>
              import('src/concerns/dossiers/components/Tasks.vue'),
            children: [
              {
                path: 'view/:taskId',
                name: 'rTaskDetails',
                component: () =>
                  import(
                    'src/concerns/dossiers/components/tasks/TaskDetails.vue'
                  ),
                props: true,
              },
              {
                path: 'create',
                name: 'rCreateTask',
                component: () =>
                  import('src/concerns/dossiers/components/CreateTask.vue'),
              },
            ],
          },
          {
            path: 'Charts',
            name: 'rDossierCharts',
            component: () =>
              import('src/concerns/dossiers/pages/DossierChartsPanel.vue'),
          },
          {
            path: 'location',
            name: 'rDossierLocation',
            component: () =>
              import('src/concerns/dossiers/pages/DossierLocation.vue'),
          },
          {
            path: 'neighbourhood',
            name: 'rDossierNeighbourhood',
            component: () =>
              import('src/concerns/dossiers/pages/DossierNeighbourhood.vue'),
          },
          {
            path: 'recent-sales',
            name: 'rRecentSales',
            component: () =>
              import('src/concerns/dossiers/pages/RecentSalesPanel.vue'),
          },
          {
            path: 'comparables',
            name: 'rComparables',
            component: () =>
              import('src/concerns/dossiers/pages/ComparablesPanel.vue'),
          },
          {
            path: 'comparables/:assetsComparisonUuid',
            name: 'rComparableLaunch',
            component: () =>
              import(
                'src/concerns/dossiers/containers/DossiersComparableContainer.vue'
              ),
            redirect: { name: 'rComparableSbs' }, // Add this line to redirect

            // path: '/dossiers/:dossierUuid',
            // name: 'rDossierContainer',
            // component: () =>
            //   import('src/concerns/dossiers/containers/DossiersMainContainer.vue'),
            children: [
              {
                path: 'solo/test',
                name: 'rComparableSoloTest',
                component: () =>
                  import(
                    'src/concerns/dossiers/components/SingleComparisonTest.vue'
                  ),
              },
              {
                path: 'solo/test2',
                name: 'rComparableSoloTest2',
                component: () =>
                  import(
                    'src/concerns/dossiers/components/SingleComparisonTest2.vue'
                  ),
              },
              {
                path: 'sbs/overview',
                name: 'rComparableSbs',
                component: () =>
                  import(
                    'src/concerns/dossiers/pages/ComparableFocusPanel.vue'
                  ),
              },
              {
                path: 'solo/overview',
                name: 'rComparableSolo',
                component: () =>
                  import(
                    'src/concerns/dossiers/pages/ComparableFocusPanel.vue'
                  ),
              },
              {
                path: 'solo/photos',
                name: 'rComparableSoloPhotos',
                component: () =>
                  import(
                    'src/concerns/dossiers/pages/ComparableFocusPanel.vue'
                  ),
              },
              {
                path: 'sbs/parts',
                name: 'rComparableSbsParts',
                component: () =>
                  import(
                    'src/concerns/dossiers/pages/ComparableFocusPanel.vue'
                  ),
              },
            ],
          },
          {
            path: 'comparables/:assetsComparisonUuid/sbs/photos',
            name: 'rComparableSbsPhotos',
            component: () =>
              import('src/concerns/dossiers/pages/ComparableFocusPanel.vue'),
          },
          {
            path: 'comparables/:assetsComparisonUuid/map',
            name: 'rComparableSbsMap',
            component: () =>
              import('src/concerns/dossiers/pages/ComparableFocusPanel.vue'),
          },
          // other potential routes:
          // epc, audio-summary, neighbourhood, floorplans, photos, etc
          // neighbourhood would have the most potential
          // Would be great to be able to do some google earth or streetview
          // cast recorded as a video...
        ],
      },
    ],
  },
  {
    path: '/curated-neighbourhoods',
    component: () => import('src/concerns/hpg/layouts/HpgLayout.vue'),
    // component: () => import('src/concerns/psq/layouts/PsqSubdomainLayout.vue'),
    meta: {
      title: 'Curated Neighbourhoods - Local Area Property Insights',
      description:
        'Explore curated neighborhoods with detailed property market analysis, local insights, and area statistics.',
      keywords:
        'neighborhoods, local areas, property markets, area analysis, postcode data',
      ogType: 'website',
      ogImage:
        'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
    },
    // import('src/concerns/user-dash/layouts/H2cUserDashLayout.vue'),
    children: [
      {
        path: '',
        name: 'rCuratedNeighbourhoods',
        component: () =>
          import(
            'src/concerns/postcode-clusters/pages/PostcodeClustersPage.vue'
          ),
        meta: {
          title: 'Curated Neighbourhoods - Explore Local Property Markets',
          description:
            'Browse our curated collection of neighborhoods with comprehensive property market data and local insights.',
          keywords:
            'curated neighbourhoods, local property markets, area insights, postcode clusters',
        },
      },
      {
        path: 'sold',
        name: 'rSoldProperties',
        component: () =>
          // import('src/concerns/gmaps/pages/PostCodeMapThird.vue'),
          import(
            'src/concerns/postcode-clusters/pages/SoldTransactionsPage.vue'
            // 'src/concerns/postcode-clusters/components/SoldTransactionsMap.vue'
          ),
      },
      {
        path: 'a/:areaClusterUuid',
        name: 'rAreaCluster',
        component: () =>
          // import('src/concerns/gmaps/pages/PostCodeMapThird.vue'),
          import(
            'src/concerns/postcode-clusters/pages/PostcodeClusterDetailsPage.vue'
          ),
      },
    ],
  },
  {
    path: '/bolt',
    // some example pages created by AIs such as bolt...
    name: 'rBoltRoot',
    component: () =>
      import('src/concerns/gpt-experiments/layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        name: 'rBoltRootIndex',
        component: () =>
          import('src/concerns/gpt-experiments/pages/IndexPage.vue'),
      },
      {
        path: 'SyntheticComparablesByChatGptPage',
        name: 'rBoltGptIndex1',
        component: () =>
          import(
            'src/concerns/gpt-experiments/pages/SyntheticComparablesByChatGptPage.vue'
          ),
      },
      {
        path: 'SyntheticComparablesByChatGptPage2',
        name: 'rBoltGptIndex2',
        component: () =>
          import(
            'src/concerns/gpt-experiments/pages/SyntheticComparablesByChatGptPage2.vue'
          ),
      },
      {
        path: 'SyntheticComparablesForHomebuyers1',
        name: 'rSyntheticComparablesForHomebuyers1',
        component: () =>
          import(
            'src/concerns/gpt-experiments/pages/SyntheticComparablesForHomebuyers1.vue'
          ),
      },
      {
        path: 'SyntheticComparablesForHomebuyers2',
        name: 'rSyntheticComparablesForHomebuyers2',
        component: () =>
          import(
            'src/concerns/gpt-experiments/pages/SyntheticComparablesForHomebuyers2.vue'
          ),
      },
      {
        path: 'Comparablesforsellers2a',
        name: 'rComparablesforsellers2a',
        component: () =>
          import(
            'src/concerns/gpt-experiments/pages/comparablesforsellers2a.vue'
          ),
      },
      {
        path: 'Comparablesforbuyers2a',
        name: 'rComparablesforbuyers2a',
        component: () =>
          import(
            'src/concerns/gpt-experiments/pages/comparablesforbuyers2a.vue'
          ),
      },
      {
        path: 'Comparablesforsellers2b',
        name: 'rComparablesforsellers2b',
        component: () =>
          import(
            'src/concerns/gpt-experiments/pages/comparablesforsellers2b.vue'
          ),
      },
      {
        path: 'Comparablesforbuyers2b',
        name: 'rComparablesforbuyers2b',
        component: () =>
          import(
            'src/concerns/gpt-experiments/pages/comparablesforbuyers2b.vue'
          ),
      },
      {
        path: 'Comparablesforagents2a',
        name: 'rComparablesforagents2a',
        component: () =>
          import(
            'src/concerns/gpt-experiments/pages/comparablesforagents2a.vue'
          ),
      },
    ],
  },
  {
    path: '/gmaps',
    name: 'rgmaps',
    component: () =>
      import('src/concerns/user-dash/layouts/H2cUserDashLayout.vue'),
    children: [
      // {
      //   path: '',
      //   name: 'rgmapsIndex',
      //   component: () =>
      //     import('src/concerns/gmaps/pages/PostCodeMapFirst.vue'),
      // },
      // {
      //   path: 'second',
      //   name: 'rgmapsSecond',
      //   component: () =>
      //     import('src/concerns/gmaps/pages/PostCodeMapSecond.vue'),
      // },
      {
        path: 'third',
        name: 'rgmapsThird',
        component: () =>
          import('src/concerns/gmaps/pages/PostCodeMapThird.vue'),
      },
    ],
  },
  {
    path: '/maps',
    name: 'rMaps',
    component: () =>
      import('src/concerns/user-dash/layouts/H2cUserDashLayout.vue'),
    children: [
      // get this error on dokku server so commenting out below component:
      // Cannot find module '@vue-leaflet/vue-leaflet
      // {
      //   path: '',
      //   name: 'rMapsIndex',
      //   component: () => import('src/concerns/maps/pages/PostCodeMapFirst.vue'),
      // },
      // {
      //   path: 'second',
      //   name: 'rMapsSecond',
      //   component: () =>
      //     import('src/concerns/maps/pages/PostCodeMapSecond.vue'),
      // },
      {
        path: 'third',
        name: 'rMapsThird',
        component: () => import('src/concerns/maps/pages/PostCodeMapThird.vue'),
      },
      {
        path: 'fourth',
        name: 'rMapsFourth',
        component: () =>
          import('src/concerns/maps/pages/PostCodeMapFourth.vue'),
      },
      {
        path: 'fifth',
        name: 'rMapsFifth',
        component: () => import('src/concerns/maps/pages/PostCodeMapFifth.vue'),
      },
      {
        path: 'sixth',
        name: 'rMapsSixth',
        // below fails with ReferenceError: Worker is not defined
        // - made me decide to give up on openlayers
        component: () => import('src/concerns/maps/pages/PostCodeMapSixth.vue'),
      },
      {
        path: 'cv11',
        name: 'rMapsCV11',
        component: () => import('src/concerns/maps/pages/PostcodeCV11Map.vue'),
      },
    ],
  },
  {
    path: '/quests-costa',
    name: 'rUserAdminQuestsCosta',
    component: () =>
      import('src/concerns/user-dash/layouts/H2cUserDashLayout.vue'),
    children: [
      // {
      //   path: '',
      //   name: 'rQuestOverview',
      //   component: () =>
      // },
      {
        path: 'quest-start',
        name: 'rQuestStart',
        component: () =>
          import('src/concerns/quests-costa/pages/AiQuestStart.vue'),
      },
      {
        path: 'search-query/:searchQueryUuid',
        name: 'rSearchQueryForQuest',
        component: () =>
          import('src/concerns/quests-costa/pages/SearchQueryBuilder.vue'),
      },
    ],
  },
  {
    path: '/admin',
    name: 'rAdminRoot',
    component: () =>
      import('src/concerns/user-dash/layouts/H2cUserDashLayout.vue'),
  },
  {
    path: '/u/:userUuid/admin',
    name: 'rUserAdminRoot',
    component: () =>
      import('src/concerns/user-dash/layouts/H2cUserDashLayout.vue'),
    // below is from older htoc-sbd (like lynette.pwb....)
    // might check it out every so often for ideas:
    // component: () => import('layouts/H2cSubdomainsDashLayout.vue'),
    children: [
      {
        path: '',
        name: 'rAdminRootIndex',
        component: () =>
          import('src/concerns/user-dash/pages/H2cUserDashIndex.vue'),
      },
      {
        path: 'profile',
        name: 'rUserAdminProfile',
        component: () =>
          import('src/concerns/user-dash/pages/H2cUserDashProfileView.vue'),
      },
      {
        // Nov 2024 : experiment from older htoc-sbd:
        path: 'myproperties',
        name: 'rProPropertiesList',
        component: () => import('src/pages/ProPropertiesList.vue'),
      },
      {
        path: 'myproperties/:listingUuid',
        name: 'rSingleSubdomainListingContainer',
        component: () =>
          import('src/layouts/htoc-sbd/SingleSubdomainListingLayout.vue'),
        children: [
          {
            path: '',
            name: 'rSingleEvaluationView',
            component: () =>
              import(
                'src/components/listing-containers/SingleEvaluationDetails.vue'
              ),
            // component: () =>
            //   import('src/apps/htoc-sbd/pages/dash/SingleEvaluationView.vue'),
          },
          {
            path: 'e',
            name: 'rSingleEvaluationEdit',
            component: () =>
              import(
                'src/components/listing-containers/SingleEvaluationEdit.vue'
              ),
            // component: () =>
            //   import('src/apps/htoc-sbd/pages/dash/SingleEvaluationView.vue'),
          },
          // {
          //   path: 'ebob2',
          //   name: 'rSingleEvaluationEditTest',
          //   component: () =>
          //     'src/components/listing-containers/SingleEvaluationEdit.vue',
          //   // import('src/apps/htoc-sbd/pages/dash/SingleEvaluationEdit.vue'),
          // },
          // {
          //   path: "html-supply",
          //   name: "rSupplyEvaluationHtml",
          //   component: () => import("src/shared/pages/SupplyEvaluationHtml.vue"),
          // },
          // {
          //   path: "html-verify",
          //   name: "rVerifyEvaluationHtml",
          //   component: () => import("src/shared/pages/VerifyEvaluationHtml.vue"),
          // },
        ],
      },
      {
        path: 'user',
        name: 'rCurrentUser',
        component: () => import('src/pages/auth/PwbProLogin.vue'),
      },
      {
        path: 'agency',
        name: 'rAgencyEdit',
        component: () => import('pages/AgencyEdit.vue'),
        children: [
          {
            path: 'general',
            name: 'rAgencyEditGeneral',
            component: () => import('components/website/EditAgencyGeneral.vue'),
          },
          {
            path: 'location',
            name: 'rAgencyEditLocation',
            component: () => import('components/website/EditAgencyGeneral.vue'),
          },
        ],
      },
      {
        path: 'pages/:pageName',
        name: 'rPagesEdit',
        component: () => import('src/pages/PagesEdit.vue'),
        children: [
          {
            path: ':pageTabName',
            name: 'rPagesEditTab',
            component: () => import('src/components/pages/EditPageTab.vue'),
          },
          // {
          //   path: '',
          //   name: "rPagesEditSingle",
          //   component: () => import("../components/translations/EditTranslationBatch.vue"),
          //   children: [
          //   ]
          // },
        ],
      },
      {
        path: 'translations',
        name: 'rTranslationsEdit',
        component: () => import('src/pages/TranslationsEdit.vue'),
        children: [
          {
            path: ':tBatchId',
            name: 'rTranslationsEditBatch',
            component: () =>
              import('src/components/translations/EditTranslationBatch.vue'),
          },
        ],
      },
      {
        path: 'website/footer',
        name: 'rWebsiteEditFooter',
        component: () => import('src/pages/WebsiteEdit.vue'),
      },
      {
        path: 'website/settings',
        name: 'rWebsiteEdit',
        component: () => import('src/pages/WebsiteEdit.vue'),
        children: [
          {
            path: 'general',
            name: 'rWebsiteEditGeneral',
            component: () =>
              import('src/components/website/EditWebsiteGeneral.vue'),
          },
          {
            path: 'appearance',
            name: 'rWebsiteEditAppearance',
            component: () =>
              import('src/components/website/EditWebsiteGeneral.vue'),
          },
          {
            path: 'navigation',
            name: 'rWebsiteEditNavigation',
            component: () =>
              import('src/components/website/EditWebsiteNavigation.vue'),
          },
        ],
      },
      {
        path: 'properties/list/all',
        name: 'rPropertiesList',
        component: () => import('src/pages/PropertiesList.vue'),
      },
      {
        path: 'properties/s/:prop_id',
        name: 'rPropertyEdit',
        component: () => import('src/pages/PropertyEdit.vue'),
        children: [
          {
            path: ':editTabName',
            name: 'rPropertyEditTab',
            component: () =>
              import('src/components/properties/EditPropertyGeneral.vue'),
          },
        ],
      },
    ],
  },
  {
    path: '/auth',
    component: () => import('layouts/AuthLayout.vue'),
    meta: {
      title: 'Authentication - House Price Guess',
      description:
        'Sign in or create an account to access House Price Guess features and personalized property insights.',
      keywords:
        'login, sign in, create account, authentication, property squares account',
      ogType: 'website',
    },
    children: [
      {
        path: 'login',
        name: 'rPwbProLoginPage',
        component: () => import('src/pages/auth/PwbProLogin.vue'),
        meta: {
          title: 'Sign In - House Price Guess',
          description:
            'Sign in to your House Price Guess account to access personalized property insights and tools.',
          keywords: 'sign in, login, property squares, account access',
        },
      },
      // {
      //   path: 'logout',
      //   name: 'rPwbProLogoutPage',
      //   component: () => import('src/pages/auth/H2cLogout.vue'),
      // },
      {
        path: 'create-account',
        name: 'rPwbProCreateAccount',
        component: () => import('src/pages/auth/PwbProCreateAccount.vue'),
        meta: {
          title: 'Create Account - House Price Guess',
          description:
            'Create your House Price Guess account to access advanced property analysis tools and personalized insights.',
          keywords:
            'create account, sign up, register, property squares, new account',
        },
        // component: () => import("src/pages/CreateAccount.vue"),
      },
    ],
  },

  {
    path: '/i',
    name: 'rInfoPages',
    component: () => import('src/concerns/hpg/layouts/HpgLayout.vue'),
    // component: () => import('src/concerns/psq/layouts/PsqSubdomainLayout.vue'),
    meta: {
      title: 'Information Pages - House Price Guess',
      description:
        'Access helpful information pages, guides, and resources about House Price Guess and property market analysis.',
      keywords: 'information, help, guides, property squares info, resources',
    },
    // component: () => import('src/layouts/EmptyContainer.vue'),
    children: [
      {
        path: ':page_slug',
        name: 'rInfoPage',
        component: () =>
          import(
            'src/concerns/dossiers/pages/content/Htoc2025ContentPagesContainer.vue'
          ),
        meta: {
          title: 'Information Page - House Price Guess',
          description:
            'Helpful information and resources about House Price Guess services and property market analysis.',
          keywords: 'information page, help, guides, property squares',
        },
        // above copied from
        // /dev/sites-2023-spt/htoc-fe-2023-spt/src/pages/content/Htoc2025ContentPagesContainer.vue
      },
    ],
  },
  // Always leave this as last one,
  // but you can also remove it
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue'),
    meta: {
      title: 'Page Not Found - House Price Guess',
      description:
        'The page you are looking for could not be found. Please check the URL or return to the homepage.',
      robots: 'noindex, nofollow',
    },
  },
]

export default routes
