<template>
  <div class="realty-game-start-page">
    <q-no-ssr>
      <div class="max-ctr q-pa-sm rgsp">
        <!-- Loading State -->
        <div v-if="isLoading" class="loading-container text-center q-pa-xl">
          <q-spinner color="primary" size="3em" />
          <div class="q-mt-md text-h6">Loading Properties...</div>
          <div class="text-body2 text-grey-7">
            Preparing your price guessing challenge
          </div>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="error-container text-center q-pa-xl">
          <q-icon name="error" color="negative" size="3em" />
          <div class="q-mt-md text-h6 text-negative">
            Failed to Load Properties
          </div>
          <div class="text-body2 text-grey-7 q-mb-lg">{{ error }}</div>
          <q-btn color="primary" label="Try Again" @click="initializeGame" />
        </div>
        <div
          v-else-if="totalProperties < 1"
          class="loading-container text-center q-pa-xl"
        >
          <!-- <q-spinner color="primary"
                     size="3em" /> -->
          <div class="q-mt-md text-h6">
            Sorry, this price guessing challenge is not available yet
          </div>
          <div class="text-body2 text-grey-7">Please check again later</div>
        </div>
        <!-- Game Start -->
        <div v-else class="game-start-container">
          <!-- Welcome Section -->
          <div
            class="welcome-section text-center q-mb-xl"
            :style="getBackgroundStyle(realtyGameSummary)"
          >
            <!-- Background overlay for better text readability -->
            <div class="game-overlay">
              <div class="welcome-icon q-mb-lg">
                <q-icon name="home" color="white" size="4em" />
              </div>
              <h1 class="text-h3 text-weight-bold text-white q-mb-md">
                {{ gameTitle }}
              </h1>
              <p class="text-h6 text-white q-mb-lg">
                Test your property valuation skills with
                {{ totalProperties }} real properties
              </p>
              <div class="challenge-stats">
                <div class="row q-col-gutter-md justify-center">
                  <div class="col-auto">
                    <q-chip color="primary" text-color="white" icon="home">
                      {{ totalProperties }} Properties
                    </q-chip>
                  </div>
                  <div class="col-auto">
                    <q-chip color="secondary" text-color="white" icon="timer">
                      ~{{ Math.ceil(totalProperties * 0.5) }} minutes
                    </q-chip>
                  </div>
                  <div class="col-auto">
                    <q-chip
                      color="positive"
                      text-color="white"
                      icon="emoji_events"
                    >
                      Score up to {{ totalProperties * 100 }} points
                    </q-chip>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- How It Works -->
          <q-card class="how-it-works-card q-mb-xl" flat bordered>
            <q-card-section class="q-pa-lg">
              <div class="text-h5 text-weight-medium text-center q-mb-lg">
                <q-icon name="help_outline" color="primary" class="q-mr-sm" />
                How It Works
              </div>

              <div class="row q-col-gutter-lg">
                <div class="col-12 col-md-4">
                  <div class="step-card text-center">
                    <div class="step-number">1</div>
                    <div class="step-title">View Property</div>
                    <div class="step-description">
                      See photos and details of real properties currently for
                      sale
                    </div>
                  </div>
                </div>
                <div class="col-12 col-md-4">
                  <div class="step-card text-center">
                    <div class="step-number">2</div>
                    <div class="step-title">Make Your Guess</div>
                    <div class="step-description">
                      Estimate the asking price based on what you see
                    </div>
                  </div>
                </div>
                <div class="col-12 col-md-4">
                  <div class="step-card text-center">
                    <div class="step-number">3</div>
                    <div class="step-title">Get Your Score</div>
                    <div class="step-description">
                      See how close you were and compare with other players
                    </div>
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>

          <!-- User Info Collection -->
          <q-card class="user-info-card q-mb-xl" flat bordered>
            <q-card-section class="q-pa-lg">
              <div class="text-h6 text-weight-medium q-mb-md">
                <q-icon name="person" color="primary" class="q-mr-sm" />
                Player Information
              </div>

              <div class="row q-col-gutter-md">
                <div class="col-12 col-md-6">
                  <q-input
                    v-model="playerName"
                    label="Your name"
                    outlined
                    dense
                    placeholder="Enter your name or leave blank"
                    hint="This helps us show you how you compare to other players"
                  >
                    <template v-slot:prepend>
                      <q-icon name="person" />
                    </template>
                  </q-input>
                </div>
                <div class="col-12 col-md-6 currency-selector">
                  <q-select
                    v-model="selectedCurrency"
                    :options="currencyOptions"
                    option-label="label"
                    option-value="code"
                    emit-value
                    map-options
                    outlined
                    dense
                    label="Currency"
                    hint="Choose your preferred currency for prices"
                  >
                    <template v-slot:prepend>
                      <q-icon name="currency_exchange" />
                    </template>
                    <template v-slot:option="scope">
                      <q-item v-bind="scope.itemProps">
                        <q-item-section avatar>
                          <span class="currency-symbol">{{
                            scope.opt.symbol
                          }}</span>
                        </q-item-section>
                        <q-item-section>
                          <q-item-label>{{ scope.opt.name }}</q-item-label>
                          <q-item-label caption>{{
                            scope.opt.code
                          }}</q-item-label>
                        </q-item-section>
                      </q-item>
                    </template>
                    <template v-slot:selected>
                      <span class="selected-currency">
                        {{ selectedCurrencyData?.symbol }}
                        {{ selectedCurrencyData?.name }}
                      </span>
                    </template>
                  </q-select>
                </div>
              </div>

              <div class="text-caption text-grey-6 q-mt-md">
                Your guesses will be saved and compared with other players. No
                personal information is required.
              </div>
            </q-card-section>
          </q-card>

          <!-- Start Button -->
          <div class="start-section text-center">
            <q-btn
              color="primary"
              size="xl"
              rounded
              unelevated
              icon="play_arrow"
              label="Start Challenge"
              @click="startGame"
              class="start-button"
            />

            <div class="text-caption text-grey-6 q-mt-md">
              Click to begin your property price challenge
            </div>
          </div>
        </div>
      </div>
    </q-no-ssr>
  </div>
</template>

<script>
import { ref, computed, watch, getCurrentInstance, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useQuasar, useMeta } from 'quasar'
import { useRealtyGame } from '../composables/useRealtyGame'
import { useRealtyGameStorage } from '../composables/useRealtyGameStorage'
import { useCurrencyConverter } from '../composables/useCurrencyConverter'
import { useRealtyGameStore } from 'src/stores/realtyGame'
import axios from 'axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

export default {
  name: 'RealtyGameStartPage',

  props: {
    realtyGameSummary: {
      type: Object,
    },
    firstPropListing: {
      type: Object,
    },
    gameTitle: {
      type: String,
      required: false,
    },
    gameDefaultCurrency: {
      type: String,
      required: false,
    },
    totalProperties: {
      type: Number,
      required: false,
    },
  },

  // preFetch hook for SSR and data prefetching
  async preFetch({ currentRoute, ssrContext }) {
    console.log(
      'preFetch function called for RealtyGameStartPage! Route:',
      currentRoute.path,
      'Params:',
      currentRoute.params
    )

    const gameSlug = currentRoute.params.gameSlug

    if (!gameSlug) {
      console.warn(
        'Missing gameSlug in preFetch. Route:',
        currentRoute.path,
        'Params:',
        currentRoute.params
      )
      return
    }

    console.log('preFetch called with gameSlug:', gameSlug)

    try {
      // Fetch game data
      const gameResponse = await axios.get(
        `${pwbFlexConfig.dataApiBase}/api_public/v4/realty_game_summary/${gameSlug}`
      )

      // Store data in Pinia store for SSR persistence
      if (gameResponse.data) {
        const gameData = gameResponse.data.price_guess_inputs
        const realtyGameDetails = gameResponse.data.realty_game_details
        const gameListings =
          gameData?.game_listings?.filter(
            (game) => game.listing_details.visible === true
          ) || []

        // Update Pinia store state
        const { useRealtyGameStore } = await import('src/stores/realtyGame')
        const store = useRealtyGameStore()
        const storeData = {
          // gameListings: gameListings.map((game) => game.listing_details),
          gameTitle:
            realtyGameDetails?.game_title || 'Property Price Challenge',
          gameDesc: realtyGameDetails?.game_description || '',
          gameBgImageUrl: realtyGameDetails?.game_bg_image_url || '',
          currentProperty: null, // No specific property for start page
        }
        console.log('preFetch storing data for start page:', storeData)
        store.setRealtyGameData(storeData)

        // Store in ssrContext for meta tags fallback (if SSR is available)
        if (ssrContext) {
          ssrContext.gameData = {
            gameTitle:
              realtyGameDetails?.game_title || 'Property Price Challenge',
            gameDesc: realtyGameDetails?.game_description || '',
            gameBgImageUrl: realtyGameDetails?.game_bg_image_url || '',
            currentProperty: null,
            totalProperties: gameListings.length,
          }
          console.log(
            'preFetch ssrContext.gameData set for start page:',
            ssrContext.gameData
          )
        }
      }

      return {
        gameData: gameResponse.data,
      }
    } catch (error) {
      console.error('Prefetch error for start page:', error)
      return null
    }
  },

  setup(props) {
    const $router = useRouter()
    const $route = useRoute()
    const $q = useQuasar()

    // Get current instance to access global properties like $ahoy
    // const instance = getCurrentInstance()

    // Initialize the price guess composable
    const {
      isLoading,
      error,
      // properties,
      // gameDefaultCurrency,
      // fetchRealtyGameData
    } = useRealtyGame()

    // Initialize the storage composable
    const {
      getOrCreateSessionId,
      saveSessionData,
      getSessionData,
      saveCurrencySelection,
      getCurrencySelection,
    } = useRealtyGameStorage()

    // Initialize the currency converter
    const {
      selectedCurrency,
      availableCurrencies,
      selectedCurrencyData,
      setCurrency,
    } = useCurrencyConverter()

    // Initialize the store
    const realtyGameStore = useRealtyGameStore()

    // Local state - Initialize playerName from session data
    const playerName = ref(getSessionData().playerName || '')

    // Meta tags state
    const metaTitle = ref(
      'Start Property Price Challenge - Test Your Property Knowledge'
    )
    const metaDescription = ref(
      'Ready to test your property market knowledge? Start our interactive price guessing game and see how well you know local property values.'
    )
    const metaImage = ref(
      'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg'
    )

    // Initialize currency from session data
    const sessionData = getSessionData()
    if (sessionData.selectedCurrency) {
      // Use currency from current session data
      setCurrency(sessionData.selectedCurrency)
    } else {
      // No session currency set, check for stored currency preference
      const storedCurrency = getCurrencySelection()

      // If stored currency is just the default fallback, use game's default currency instead
      if (
        storedCurrency === 'GBP' &&
        props.gameDefaultCurrency &&
        props.gameDefaultCurrency !== 'GBP'
      ) {
        // Use game's default currency as it's more specific than the generic GBP fallback
        setCurrency(props.gameDefaultCurrency)
      } else {
        // Use stored currency selection (could be GBP if that's what user actually selected)
        setCurrency(storedCurrency)
      }
    }

    // Computed properties for currency options
    const currencyOptions = computed(() => {
      return availableCurrencies.value.map((currency) => ({
        code: currency.code,
        name: currency.name,
        symbol: currency.symbol,
        label: `${currency.symbol} ${currency.name} (${currency.code})`,
      }))
    })

    // Computed property for gameSlug
    const gameSlug = computed(() => $route.params.gameSlug)

    // Function to update meta tags
    const updateMetaTags = () => {
      // Access ssrContext.gameData during SSR (if available)
      const isSSR = typeof window === 'undefined'
      const ssrGameData =
        isSSR && $q.ssrContext?.gameData ? $q.ssrContext.gameData : null

      // Get store data
      const storeGameTitle = realtyGameStore.getGameTitle
      const storeGameDesc = realtyGameStore.getGameDesc
      const storeGameBgImageUrl = realtyGameStore.getGameBgImageUrl
      const storeTotalProperties = realtyGameStore.getTotalProperties
      const storeIsDataLoaded = realtyGameStore.getIsDataLoaded

      // Use data from multiple sources with proper fallbacks
      // Prioritize SSR data, then store data (if loaded), then props, then defaults
      const gameTitle_value =
        ssrGameData?.gameTitle ||
        (storeIsDataLoaded && storeGameTitle !== 'Property Price Challenge'
          ? storeGameTitle
          : null) ||
        props.gameTitle ||
        'Property Price Challenge'

      const gameDesc_value =
        ssrGameData?.gameDesc ||
        (storeIsDataLoaded && storeGameDesc ? storeGameDesc : null) ||
        'Test your property market knowledge with our interactive price guessing game'

      const totalProperties_value =
        ssrGameData?.totalProperties ||
        (storeIsDataLoaded ? storeTotalProperties : null) ||
        props.totalProperties ||
        0

      const title = `Start ${gameTitle_value} - Test Your Property Knowledge`

      const description = gameDesc_value
        ? gameDesc_value
        : `Ready to test your property market knowledge? Start our interactive price guessing game with ${totalProperties_value} real properties and see how well you know local property values.`

      // Update meta tag refs
      metaTitle.value = title
      metaDescription.value = description

      // Update image if available
      const gameBgImageUrl =
        ssrGameData?.gameBgImageUrl ||
        (storeIsDataLoaded && storeGameBgImageUrl
          ? storeGameBgImageUrl
          : null) ||
        'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg'

      metaImage.value = gameBgImageUrl
    }

    // Computed properties for metatags
    const metaData = computed(() => {
      // During SSR, try to get data directly from SSR context first
      const isSSR = typeof window === 'undefined'
      const ssrGameData =
        isSSR && $q.ssrContext?.gameData ? $q.ssrContext.gameData : null

      // If we have SSR data, use it directly for meta tags
      if (ssrGameData) {
        const gameTitle_value =
          ssrGameData.gameTitle || 'Property Price Challenge'
        const gameDesc_value =
          ssrGameData.gameDesc ||
          'Test your property market knowledge with our interactive price guessing game'
        const gameBgImageUrl =
          ssrGameData.gameBgImageUrl ||
          'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg'

        const title = `Start ${gameTitle_value} - Test Your Property Knowledge`
        const description = gameDesc_value

        const metaObject = {
          title,
          meta: {
            description: { name: 'description', content: description },
            keywords: {
              name: 'keywords',
              content: `property price game, real estate challenge, property valuation, house price quiz, market knowledge test, ${gameTitle_value}`,
            },
            'og:title': { property: 'og:title', content: title },
            'og:description': {
              property: 'og:description',
              content: description,
            },
            'og:type': { property: 'og:type', content: 'website' },
            'twitter:card': {
              name: 'twitter:card',
              content: 'summary_large_image',
            },
            'twitter:title': { name: 'twitter:title', content: title },
            'twitter:description': {
              name: 'twitter:description',
              content: description,
            },
            'og:image': { property: 'og:image', content: gameBgImageUrl },
            'twitter:image': { name: 'twitter:image', content: gameBgImageUrl },
          },
        }

        return metaObject
      }

      // Fallback to ref-based approach for client-side
      const metaObject = {
        title: metaTitle.value,
        meta: {
          description: { name: 'description', content: metaDescription.value },
          keywords: {
            name: 'keywords',
            content: `property price game, real estate challenge, property valuation, house price quiz, market knowledge test, ${metaTitle.value
              .replace('Start ', '')
              .replace(' - Test Your Property Knowledge', '')}`,
          },
          'og:title': { property: 'og:title', content: metaTitle.value },
          'og:description': {
            property: 'og:description',
            content: metaDescription.value,
          },
          'og:type': { property: 'og:type', content: 'website' },
          'twitter:card': {
            name: 'twitter:card',
            content: 'summary_large_image',
          },
          'twitter:title': { name: 'twitter:title', content: metaTitle.value },
          'twitter:description': {
            name: 'twitter:description',
            content: metaDescription.value,
          },
          'og:image': { property: 'og:image', content: metaImage.value },
          'twitter:image': { name: 'twitter:image', content: metaImage.value },
        },
      }

      // Add og:url on client side
      if (typeof window !== 'undefined') {
        metaObject.meta['og:url'] = {
          property: 'og:url',
          content: window.location.href,
        }
      }

      return metaObject
    })

    // Use meta tags
    useMeta(() => metaData.value)

    // Initialize meta tags with nextTick to ensure store is hydrated
    nextTick(() => {
      updateMetaTags()
    })

    // Watch for store data changes to update meta tags
    watch(
      () => realtyGameStore.getIsDataLoaded,
      (isLoaded) => {
        if (isLoaded) {
          // Force meta tag update when store data is loaded
          nextTick(() => {
            updateMetaTags()
          })
        }
      },
      { immediate: true }
    )

    // Watch for props changes to update meta tags
    watch(
      [
        () => props.gameTitle,
        () => props.gameDefaultCurrency,
        () => props.totalProperties,
      ],
      () => {
        updateMetaTags()
      }
    )

    // Watch for store data changes
    watch(
      [
        () => realtyGameStore.getGameTitle,
        () => realtyGameStore.getGameDesc,
        () => realtyGameStore.getGameBgImageUrl,
        () => realtyGameStore.getTotalProperties,
      ],
      () => {
        if (realtyGameStore.getIsDataLoaded) {
          updateMetaTags()
        }
      }
    )

    // const firstPropListing = computed(() => {
    //   return properties.value.find(prop => prop.visible === true)
    // })

    // Watch playerName and save to session data when it changes
    watch(playerName, (newValue) => {
      saveSessionData({
        playerName: newValue || 'Anonymous Player',
        totalProperties: props.totalProperties || 0,
        gameTitle: props.gameTitle || 'Property Price Challenge',
        startedAt: new Date().toISOString(),
      })
    })

    // Watch selectedCurrency and save to session data when it changes
    watch(selectedCurrency, (newValue) => {
      if (newValue) {
        saveCurrencySelection(newValue)
        saveSessionData({
          selectedCurrency: newValue,
        })
      }
    })

    // Methods
    const getBackgroundStyle = (game) => {
      const baseStyle = {
        padding: '0px',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
      }

      if (game.game_bg_image_url) {
        return {
          ...baseStyle,
          backgroundImage: `url(${game.game_bg_image_url})`,
        }
      }

      // Fallback gradient based on game title or default
      const gradients = [
        'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
        'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
      ]

      const index = gameSlug.value
        ? gameSlug.value.charCodeAt(0) % gradients.length
        : 0

      return {
        ...baseStyle,
        background: gradients[index],
      }
    }

    const initializeGame = async () => {
      // try {
      //   await fetchRealtyGameData()
      // } catch (err) {
      //   $q.notify({
      //     color: 'negative',
      //     message: 'Failed to load property data',
      //     icon: 'error'
      //   })
      // }
    }

    // Random name generator
    const generateRandomName = () => {
      const adjectives = [
        'Clever',
        'Smart',
        'Wise',
        'Sharp',
        'Keen',
        'Bright',
        'Quick',
        'Swift',
        'Bold',
        'Brave',
        'Cool',
        'Lucky',
        'Happy',
        'Sunny',
        'Friendly',
        'Kind',
        'Creative',
        'Artistic',
        'Musical',
        'Sporty',
        'Active',
        'Energetic',
        'Dynamic',
        'Curious',
        'Adventurous',
        'Daring',
        'Fearless',
        'Confident',
        'Optimistic',
      ]

      const nouns = [
        'Player',
        'Gamer',
        'Explorer',
        'Detective',
        'Expert',
        'Analyst',
        'Investor',
        'Buyer',
        'Agent',
        'Scout',
        'Hunter',
        'Seeker',
        'Finder',
        'Discoverer',
        'Challenger',
        'Champion',
        'Winner',
        'Star',
        'Hero',
        'Ace',
        'Pro',
        'Wizard',
        'Master',
        'Genius',
        'Ninja',
        'Warrior',
        'Guardian',
        'Knight',
      ]

      const randomAdjective =
        adjectives[Math.floor(Math.random() * adjectives.length)]
      const randomNoun = nouns[Math.floor(Math.random() * nouns.length)]
      const randomNumber = Math.floor(Math.random() * 999) + 1

      return `${randomAdjective} ${randomNoun} ${randomNumber}`
    }

    const startGame = () => {
      if (
        !playerName.value ||
        playerName.value.trim() === '' ||
        playerName.value === 'Anonymous Player'
      ) {
        const randomName = generateRandomName()

        $q.dialog({
          title: 'Choose Your Player Name',
          message: `
            <p>We've generated a random name for you: <strong>${randomName}</strong></p>
            <p>You still have a chance to personalise your player name though.</p>
            <p><em>Creating your own name makes leaderboard comparisons more meaningful, and adds a personal touch to your experience.</em></p>
          `,
          html: true,
          prompt: {
            model: randomName,
            type: 'text',
            label: 'Your player name',
            placeholder: 'Enter your preferred name',
          },
          ok: {
            label: "Let's Go!",
            color: 'primary',
          },
          persistent: true,
        }).onOk((name) => {
          playerName.value = name.trim() || randomName
          proceedWithGame()
        })

        return
      }

      proceedWithGame()
    }

    const proceedWithGame = () => {
      // Get or create session ID from storage
      const gameSessionId = getOrCreateSessionId()

      // Save session metadata
      saveSessionData({
        playerName: playerName.value || 'Anonymous Player',
        totalProperties: props.totalProperties || 0,
        gameTitle: props.gameTitle || 'Property Price Challenge',
        selectedCurrency: selectedCurrency.value,
        startedAt: new Date().toISOString(),
      })

      // Save currency selection
      saveCurrencySelection(selectedCurrency.value)

      // Navigate to first property with session ID
      // Get the first visible property UUID
      // const locFirstProperty = properties.value.find(prop => prop.visible === true)
      if (props.firstPropListing) {
        $router.push({
          name: 'rPriceGameProperty',
          params: {
            gameSlug: $route.params.gameSlug,
            propertyUuid: props.firstPropListing.listing_details.uuid,
          },
          query: {
            session: gameSessionId,
            ...(playerName.value && { name: playerName.value }),
          },
        })
      } else {
        $q.notify({
          color: 'negative',
          message: 'No properties available for this game',
          icon: 'error',
        })
      }
    }

    return {
      // Reactive state
      playerName,
      selectedCurrency,
      availableCurrencies,
      selectedCurrencyData,

      // Computed properties
      gameSlug,
      currencyOptions,
      metaData,

      // Composable methods and state
      isLoading,
      error,
      realtyGameStore,

      // Storage methods
      getOrCreateSessionId,
      saveSessionData,
      getSessionData,
      saveCurrencySelection,
      getCurrencySelection,

      // Currency methods
      setCurrency,

      // Local methods
      getBackgroundStyle,
      initializeGame,
      generateRandomName,
      startGame,
      proceedWithGame,
    }
  },
}
</script>

<style scoped>
.game-overlay {
  position: relative;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    rgba(0, 0, 0, 0.7) 100%
  );
  /* display: flex; */
  /* align-items: flex-start; */
  padding: 32px;
  height: 100%;
  border-radius: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .game-overlay {
    padding: 24px;
    min-height: 400px;
    /* Ensure enough space for content */
  }
}

@media (max-width: 480px) {
  .game-overlay {
    padding: 20px;
    min-height: 450px;
    /* Slightly more space for smaller screens */
  }
}

.realty-game-start-page {
  min-height: 100vh;
}

.max-ctr {
  max-width: 800px;
  margin: 0 auto;
}

.loading-container,
.error-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.game-start-container {
  padding-top: 2rem;
}

.welcome-section {
  background: white;
  border-radius: 12px;
  padding: 3rem 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.welcome-icon {
  border-radius: 50%;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.challenge-stats {
  margin-top: 2rem;
}

.how-it-works-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.step-card {
  padding: 1.5rem;
}

.step-number {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0 auto 1rem;
}

.step-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}

.step-description {
  color: #666;
  line-height: 1.5;
}

.user-info-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.start-section {
  background: white;
  border-radius: 12px;
  padding: 3rem 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.start-button {
  padding: 1rem 3rem;
  font-size: 1.2rem;
  font-weight: 600;
  text-transform: none;
}

.currency-symbol {
  font-weight: bold;
  font-size: 1.1rem;
  min-width: 30px;
  text-align: center;
}

.selected-currency {
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .welcome-section {
    padding: 2rem 1rem;
  }

  .start-section {
    padding: 2rem 1rem;
  }

  .start-button {
    width: 100%;
    max-width: 300px;
  }
}
</style>
